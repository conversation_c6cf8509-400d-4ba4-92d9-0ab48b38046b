using Fish.PlayMaker;
using HutongGames.PlayMakerEditor;
using UnityEngine;

namespace GameScript.Editor.PlayMaker
{
    [CustomActionEditor(typeof(SendNetworkAction))]
    public class SendNetworkActionEditor : CustomActionEditor
    {
        public override bool OnGUI()
        {
            var action = target as SendNetworkAction;
            
            EditField("Cmd");
            EditField("Action");
            
            EditField("UseUserId");
            EditField("UseGameId");
            EditField("UseClientId");
            EditField("UseRoomId");
            EditField("UseTableId");

            
            
           return GUI.changed;
        }
    }
}