using System;
using System.Reflection;
using HutongGames.PlayMaker;

namespace Fish.PlayMaker
{
    [ActionCategory("Fish/Common")]
    [HutongGames.PlayMaker.Tooltip("创建一个对象")]
    public class CreateCLRInstanceAction : FsmStateAction
    {
        [HutongGames.PlayMaker.Tooltip("是否是主工程类型")]
        public bool isMainAssemblyType;

        [RequiredField] [HutongGames.PlayMaker.Tooltip("类名")]
        public string className;

        [HutongGames.PlayMaker.Tooltip("绑定标记")]
        public BindingFlags[] flags = new BindingFlags[] { BindingFlags.Instance, BindingFlags.Public };

        [HutongGames.PlayMaker.Tooltip("函数参数类型列表")]
        public EMethodParamType[] parameterTypes = new EMethodParamType[0];

        [HutongGames.PlayMaker.Tooltip("参数列表")]
        public FsmVar[] parameters = new FsmVar[0];

        [HutongGames.PlayMaker.Tooltip("是否存储到变量")]
        public bool SaveToVariable;

        [HutongGames.PlayMaker.Tooltip("存储对象")]
        public FsmVar StoreVariable = new FsmVar();

        [HutongGames.PlayMaker.Tooltip("Store的key")]
        public string StoreKey;

        public override void OnEnter()
        {
            Process();

            Finish();
        }

        private void Process()
        {
            object[] param = new object[parameters.Length];
            for (int i = 0; i < parameters.Length; i++)
            {
                var pa = parameters[i];
                if (pa.useVariable)
                {
                    pa.GetValueFrom(pa.NamedVar);
                }

                param[i] = pa.GetValue();
            }

            var ret = ILRManager.Instance.CreateInstance(className, PlayMakerUtility.GetBindingFlag(flags),
                isMainAssemblyType, param, PlayMakerUtility.GetParameterTypes(parameterTypes));

            if (SaveToVariable)
            {
                StoreVariable.SetValue(ret);
            }
            else
            {
                Owner.gameObject.SetDataObject(StoreKey, ret);
            }
        }
    }
}