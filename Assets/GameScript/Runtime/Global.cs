using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Newtonsoft.Json.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Fish
{
    public class Global : MonoBehaviour
    {
        static Global _inst;
        public static Global inst
        {
            get
            {
                if (_inst == null)
                {
                    GameObject ga = new GameObject("Global");
                    _inst = ga.AddComponent<Global>();
                    _inst.Init();
                    DontDestroyOnLoad(ga);

                    ga.AddComponent<UI>();
                    ga.AddComponent<FPS>();
                    ga.AddComponent<SortingNodeManager>();
                    ga.AddComponent<View>();
                }
                return _inst;
            }
        }

        public static bool verified
        {
            get
            {
                return Certificate.inst.isCertification;
            }
            set
            {
                Certificate.inst.isCertification = value;
            }
        }

        public static int nextCheckTime
        {
            set
            {
                Certificate.inst.nextCheckTime = value;
            }
            get
            {
                return Certificate.inst.nextCheckTime;
            }
        }

        // 实名认证上传间隔
        public static int checkInterval
        {
            set
            {
                Certificate.inst.checkInterval = value;
            }
            get
            {
                return Certificate.inst.checkInterval;
            }
        }

        // 测试，仿真，模拟，线上,海外测试，海外仿真
        public readonly static List<string> GameServerList = new List<string>()
        {
            "https://3-u3dfish-sdk-test01.tuyoo.com/", // 测试
            "https://3-fish3d-sdk-sim01.tuyoo.com/",            // 仿真
            "https://fishlinetest.tuyoo.com/",         // 模拟
            "https://fish.tuyoo.com/",                 // 线上
            "https://hwslotstest.nalrer.cn/",          // 海外测试
            "https://hwfish3dfz.nalrer.cn/"            // 海外仿真
        };

        public readonly static List<string> SDKServerList = new List<string>()
        {
            "https://3-u3dfish-sdk-test01.tuyoo.com/",
            "https://3-fish3d-sdk-sim01.tuyoo.com/",
            "https://fishlinetest.tuyoo.com/",
            "https://fish.tuyoo.com/",
            "https://hwslotstest.nalrer.cn/",
            "https://hwfish3dfz.nalrer.cn/"
        };

#if FISH_MJ
        public static string packageName = "com.tuyoo.dwby3d.official";
#else
        public static string packageName = "com.tuyoo.fish3d.official";
#endif

        //ILR中使用
#if UNITY_HW
        public static bool isHW = true;
#else
        public static bool isHW = false;
#endif

        //CN,EN,TW,TH......
        public static string lang = "CN";

        // 线上CDN
        public static string cdnUrl1 = "https://fishapp-hw.tuyougame.com/u3d/release/";
        public static string cdnUrl2 = "https://fish5qn.naler.cn/u3d/release/";
        public static string cdnVer = "1.506n";
        public static int buildVer = 0;
        public static string fishResourceVer = null;
        public static string baseResourceVer = null;
        
        // 线下CDN
        //"https://package-inner.tuyoo.com/artifacts/unity/fish3d/1.230/";


        // platform
#if UNITY_EDITOR_WIN || UNITY_STANDALONE_WIN
        public static string platform = "windows";
#elif UNITY_EDITOR_OSX || UNITY_STANDALONE_OSX
        public static string platform = "mac";
#elif UNITY_ANDROID
        public static string platform = "android";
#elif UNITY_IOS
        public static string platform = "ios";
#elif UNITY_OPENHARMONY
        public static string platform = "openharmony";
#endif
        public static string displayVersion = "v1.290";
        public static string clientId
        {
            get
            {
                return inst._clientId;
            }
            set
            {
                inst._clientId = value;
            }
        }
        public static string serverUrl
        {
            set
            {
                sdkUrl = value;
                srvUrl = value;
            }
        }

        public static string authorCode = "";
        public static string token = "";
        public static string jwttoken = "";
        public static string userPwd = "";
        public static JArray userList;

        // public static bool wmywbyt = false;
        // public static bool tymwwmt = false;
        public static string loginTypeLeft = "tyAccount";
        public static string loginTypeRight = "";
        public static string loginTypeAuto = "tyAccount";
        public static string realNameSdk = "tuyoo";
        public static string exitSdk = "";

        public static bool disableCSGroup = false;
        public static bool disableCSButton = false;

        public static bool enableHandleEventException = true;

        public static bool enableYidunCaptcha = true;

#if FISH_INTERNATIONAL
        // 主渠道 
        string _clientId = "Android_5.37_tyGuest,facebook.googleplay.0-hall28.googleplay.my";
        public static string bundleId = "com.hwtu.cscfish";
        public static int cloudId = 94;
        public static int gameId = 9999;
        public static int fishGameId = 28;
        public static int sdkGameID = 20481;
        public static bool forSA = false;
        public static string privacyUrl = "http://arksgame.com/privacyGoogle.html";
        public static string protocolUrl = "http://arksgame.com/TermsOfService.html";
        public static string childPrivacyUrl = "https://downqn.tuyoo.com/agreement/ty/ty_18_policy.html";
        public static string sdkUrl = "https://hwslotstest.nalrer.cn/";
        public static string srvUrl = "https://hwslotstest.nalrer.cn/";

#elif FISH_MJ
        string _clientId = "Android_7.0302_tyGuest,tyAccount.alipay,yinlian.0-hall28.official.dwby3d";
        public static int cloudId = 118;
        public static int gameId = 9999;
        public static int fishGameId = 28;
        public static int sdkGameID = 20531;
        public static string privacyUrl = "https://fish5qn.naler.cn/u3d/web/privacy.html";
        public static string protocolUrl = "https://fish5qn.naler.cn/u3d/web/protocol.html";
        public static string childPrivacyUrl = "https://downqn.tuyoo.com/agreement/ty/ty_18_policy.html";

        // 马甲（测试服）
        //public static string sdkUrl = "https://fishu3dvbtest.tuyoo.com/";
        //public static string srvUrl = "https://fishu3dvbtest.tuyoo.com/";

        // 马甲（仿真服）
        public static string sdkUrl = "https://118-vgfish-sdk-sim01.tytuyoo.com/";
        public static string srvUrl = "https://118-vgfish-sdk-sim01.tytuyoo.com/";

        // 马甲（模拟服）
        // public static string sdkUrl = "https://118-vgfish-sdk-moni01.tytuyoo.com/";
        // public static string srvUrl = "https://118-vgfish-sdk-moni01.tytuyoo.com/";

        // 马甲（正式服）
        // public static string sdkUrl = "https://118-vgfish-sdk-online01.tytuyoo.com/";
        // public static string srvUrl = "https://118-vgfish-sdk-online01.tytuyoo.com/";

#else
        //string _clientId = "IOS_5.240_weixin,tuyoo.appStore.0-hall28.appStore.bgbybxg";
        //string _clientId = "Android_5.3021_tyGuest,tyAccount,yidunlogin.weixinPay,alipay,yinlian,jingdong,weixinShare.0-hall28.official.fish3d";
        string _clientId = "Android_5.507_tyGuest,tyAccount,yidunlogin.weixinPay,alipay,yinlian,jingdong.0-hall28.official.fish3d";
        public static int cloudId = 3;
        public static int gameId = 9999;
        public static int fishGameId = 28;
        public static int sdkGameID = 10010;
        public static string privacyUrl = "https://downqn.tuyoo.com/agreement/ty/newPrivacy.html";
        public static string protocolUrl = "https://downqn.tuyoo.com/agreement/ty/Terms_of_Service.html";
        public static string childPrivacyUrl = "https://downqn.tuyoo.com/agreement/ty/ty_18_policy.html";
        // // 老测试服
        // public static string sdkUrl = "https://fishmixtest2.tuyoo.com/";
        // public static string srvUrl = "http://**************:8000/";

        // 数值膨胀测试服
        //public static string sdkUrl = "http://3-u3dfish-sdk-test02.tuyoo.com/";
        //public static string srvUrl = "http://3-u3dfish-sdk-test02.tuyoo.com/";

        // 测试服
        public static string sdkUrl = "https://3-u3dfish-sdk-test01.tuyoo.com/";
        public static string srvUrl = "https://3-u3dfish-sdk-test01.tuyoo.com/";

        // 仿真服
        //public static string sdkUrl = "https://fishmixfz.tuyoo.com/";
        //public static string srvUrl = "https://fishmixfz.tuyoo.com/";

        // 模拟服
        //public static string sdkUrl = "https://fishlinetest.tuyoo.com/";
        //public static string srvUrl = "https://fishlinetest.tuyoo.com/";

        // 线上服
        //public static string sdkUrl = "https://fish.tuyoo.com/";
        //public static string srvUrl = "https://fish.tuyoo.com/";

        // 海外测试服
        // public static string sdkUrl = "https://hwslotstest.nalrer.cn/";
        // public static string srvUrl = "https://hwslotstest.nalrer.cn/";
        // 海外仿真服
        // public static string sdkUrl = "https://hwfish3dfz.nalrer.cn/";
        // public static string srvUrl = "https://hwfish3dfz.nalrer.cn/";
#endif

        string _currentScene = "";
        SDKInterface sdkInterface = null;
        public static string currentScene
        {
            get { return inst._currentScene; }
            set { inst._currentScene = value; }
        }

        public static bool manifestLoaded = false;
        public static bool loadConfigFlag = false;
        public static bool loadLuaFlag = false;
        public static bool quit = false;
        public static string SystemLanguage;
#if FISH_PLUGIN
        public static bool pluginExit = false;
#endif
        void Init()
        {
            nextCheckTime = (int)Time.time + Global.checkInterval;
            // 帧率
            QualitySettings.vSyncCount = 0;

            Application.runInBackground = true;
#if (UNITY_ANDROID)
            Application.targetFrameRate = 40;
#else
            Application.targetFrameRate = 60;
#endif
            SystemLanguage = Application.systemLanguage.ToString();

#if UNITY_IPHONE && !FISH_PLUGIN
            BuglyAgent.InitWithAppId ("ef70efc42d");
            BuglyAgent.EnableExceptionHandler();            
#endif
            sdkInterface = Framework4Unity.Instance.sdkInterface;
        }
        void Update()
        {
            NetCenter.inst.Update();
            EventCenter.inst.Update();
            GActionManager.inst.Update();

#if UNITY_STANDALONE
            if (!quit && Input.GetKey("escape")) 
            {
                quit = true;
                MessageBox.Inst.ShowWithDestroy(
                    ()=>{
                        Debug.LogWarning("Quit application");
                         Application.Quit();
                    },
                    ()=>{
                        Debug.LogWarning("Quit application canceld");
                    },
                    "是否要退出游戏？",
                    ()=>{
                        quit = false;
                    });
            }
#endif

#if FISH_PLUGIN
            if (pluginExit)
            {
                pluginExit = false;
                Framework4Unity.Instance.sdkInterface.ExitGame("plugin");
            }
#endif
        }

        /// <summary>
        /// 开始游戏
        /// </summary>
        public void StartGame()
        {
            Position.Init();
            LoadConfig();

            NetCenter.inst.Start();

            //手机剩余空间打点
            long freeSpace = Framework4Unity.Instance.sdkInterface.GetDeviceAvailableSpace();
            freeSpace /= (1024 * 1024);
            BI.Inst.ReportBi(BIEvents.SYSTEM_FREE_SPACE, "FreeSpace", freeSpace);
        }
        private void LoadConfig()
        {
            // if (!loadConfigFlag)
            {
                Config.Init();
                FishType.Init();
                Path.Init();
                Track.Init();
                loadConfigFlag = true;
            }
        }
    }
}